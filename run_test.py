import asyncio
import ccxt.pro as ccxt
from research.runner import Runner
from research.config import config as cfg

if __name__ == "__main__":
    async def main():
        # 要监控的交易对
        symbol = "H/USDT:USDT"
        
        print(f"🚀 准备监控交易对: {symbol}")
        
        # 初始化Maker交易所 (Bybit)
        maker = ccxt.bybit({
            'apiKey': cfg.exchanges_config['bybit']['apiKey'],
            'secret': cfg.exchanges_config['bybit']['secret'],
            'enableRateLimit': True,
            'options': {'defaultType': 'swap'}
        })
        maker.set_sandbox_mode(False)
        print("✅ Bybit (Maker) 交易所初始化完成")

        # 初始化Taker交易所 (OKX)
        taker = ccxt.okx({
            'apiKey': cfg.exchanges_config['okx']['apiKey'],
            'secret': cfg.exchanges_config['okx']['secret'],
            'password': cfg.exchanges_config['okx']['password'],
            'enableRateLimit': True, 
            'options': {'defaultType': 'swap'}
        })
        taker.set_sandbox_mode(False)
        print("✅ OKX (Taker) 交易所初始化完成")
        
        # 创建Runner实例（现在需要传入symbol）
        runner = Runner(symbol, maker, taker)
        
        # 添加自定义价格更新回调
        def on_price_update(price_data):
            """自定义价格更新处理"""
            print(f"📈 价格更新 - {len(price_data)} 个交易所数据已更新")
            
            # 计算套利机会
            arb_opp = runner.calculate_arbitrage_opportunity()
            if arb_opp and (arb_opp['profitable_open'] or arb_opp['profitable_close']):
                print(f"⚡ 实时套利信号:")
                if arb_opp['profitable_open']:
                    print(f"   🔥 开仓机会: {arb_opp['open_spread_percent']:.4f}%")
                if arb_opp['profitable_close']:
                    print(f"   💰 平仓机会: {arb_opp['close_spread_percent']:.4f}%")
        
        runner.add_price_callback(on_price_update)
        
        try:
            # 使用包含价格监控的运行方法
            await runner.run_with_price_monitoring()
        finally:
            # 关闭交易所连接
            await maker.close()
            await taker.close()
            print("🔌 交易所连接已关闭")
    
    asyncio.run(main())