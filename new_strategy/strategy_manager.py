"""
策略管理器 - 协调各个模块运行
"""
import asyncio
from typing import Dict, Any
from new_strategy.market_data import MarketDataManager
from new_strategy.strategy_core import CrossMarketStrategy

class StrategyManager:
    """策略管理器"""
    
    def __init__(self, config):
        self.config = config
        self.market_manager = MarketDataManager(config.exchanges_config)
        self.strategy = CrossMarketStrategy(config, self.market_manager, self)  # 传递自身引用
        self.running = False
    
    async def initialize(self):
        """初始化策略管理器"""
        try:
            await self.market_manager.initialize()
            await self.strategy.initialize()  # 初始化策略（包括订单管理器）
            print("✅ 策略管理器初始化完成")
        except Exception as e:
            print(f"❌ 策略管理器初始化失败: {e}")
            raise
    
    async def start(self):
        """启动策略"""
        self.running = True
        
        print("🚀 启动新版本套利策略 (基于ccxtpro + 协程)")
        print(f"📈 Maker市场: {self.config.maker_exchange} - {self.config.maker_symbol}")
        print(f"📉 Taker市场: {self.config.taker_exchange} - {self.config.taker_symbol}")
        print(f"💰 单次交易量: {self.config.single_maker_amt}")
        print(f"📊 价差阈值: {self.config.spread_limit} ({self.config.spread_limit*100:.3f}%)")
        print(f"🔍 价差检查次数: {self.config.spread_check_times}")
        print(f"⏱️ 检查间隔: {self.config.spread_check_delay}s")
        print(f"🎯 模拟模式: {'开启' if self.config.simulate_only else '关闭'}")
        
        # 配置要监控的市场
        symbols_to_watch = [
            {
                'exchange': self.config.maker_exchange,
                'symbol': self.config.maker_symbol
            },
            {
                'exchange': self.config.taker_exchange, 
                'symbol': self.config.taker_symbol
            }
        ]
        
        try:
            # 启动市场数据监控
            await self.market_manager.start_monitoring(symbols_to_watch)
        except KeyboardInterrupt:
            print("🛑 收到退出信号")
        except Exception as e:
            print(f"❌ 策略运行异常: {e}")
        finally:
            await self.stop()
    
    async def stop(self):
        """停止策略"""
        if not self.running:
            return
            
        self.running = False
        print("🔄 正在停止策略...")
        
        try:
            # 停止市场数据监控
            await self.market_manager.stop_monitoring()
            print("✅ 策略已安全停止")
        except Exception as e:
            print(f"❌ 停止策略时发生异常: {e}")
    
    def force_stop(self):
        """强制停止策略（用于下单成功后的快速退出）"""
        self.running = False
        print("🛑 强制停止策略（下单成功）") 