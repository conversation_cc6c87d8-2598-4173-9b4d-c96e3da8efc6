"""
市场数据监控模块 - 使用ccxtpro的watch功能
"""
import asyncio
import ccxt.pro as ccxt
from typing import Dict, Any, Optional, Callable
from dataclasses import dataclass
import time

@dataclass
class MarketData:
    """市场数据结构"""
    symbol: str
    exchange: str
    orderbook: Optional[Dict] = None
    timestamp: float = 0
    contract_info: Optional[Dict] = None  # 合约信息（张数、最小数量等）
    
    def get_best_bid(self) -> float:
        """获取最佳买价"""
        if self.orderbook and 'bids' in self.orderbook and len(self.orderbook['bids']) > 0:
            return self.orderbook['bids'][0][0]
        return 0
    
    def get_best_ask(self) -> float:
        """获取最佳卖价"""
        if self.orderbook and 'asks' in self.orderbook and len(self.orderbook['asks']) > 0:
            return self.orderbook['asks'][0][0]
        return 0
    
    def get_contract_amount(self, usdt_amount: float) -> float:
        """根据USDT金额计算合约张数"""
        if not self.contract_info:
            return usdt_amount  # 如果没有合约信息，直接返回USDT金额
        
        # 获取合约面值
        contract_size = self.contract_info.get('contractSize', 1.0)
        # 获取最小数量
        min_amount = self.contract_info.get('limits', {}).get('amount', {}).get('min', 0.1)
        
        # 计算张数
        if contract_size > 0:
            contracts = usdt_amount / contract_size
            # 确保不小于最小数量
            if contracts < min_amount:
                contracts = min_amount
            return contracts
        
        return usdt_amount

class MarketDataManager:
    """市场数据管理器"""
    
    def __init__(self, exchanges_config: Dict[str, Dict]):
        self.exchanges_config = exchanges_config
        self.exchanges: Dict[str, ccxt.Exchange] = {}
        self.market_data: Dict[str, MarketData] = {}
        self.callbacks: list = []
        self.running = False
        
    async def initialize(self):
        """初始化交易所连接"""
        try:
            # 初始化Bybit (永续合约) 🔥
            bybit_config = self.exchanges_config['bybit'].copy()
            bybit_config.update({
                'enableRateLimit': True,
                'options': {
                    'defaultType': 'swap',  # 🔥 永续合约！
                    'loadMarketsTimeout': 10000,
                    'fetchMarketsWs': False,  # 禁用WebSocket加载市场
                }
            })
            self.exchanges['bybit'] = ccxt.bybit(bybit_config)
            
            # 设置沙盒模式
            if bybit_config.get('sandbox', False):
                self.exchanges['bybit'].set_sandbox_mode(True)
            print("✅ Bybit连接初始化完成 (永续合约) 🔥")
            
            # 初始化OKX (永续合约)
            okx_config = self.exchanges_config['okx'].copy()
            okx_config.update({
                'enableRateLimit': True,
                'options': {
                    'defaultType': 'swap',  # 永续合约
                    'loadMarketsTimeout': 10000,
                    'fetchMarketsWs': False,  # 禁用WebSocket加载市场
                }
            })
            self.exchanges['okx'] = ccxt.okx(okx_config)
            
            # 设置沙盒模式
            if okx_config.get('sandbox', False):
                self.exchanges['okx'].set_sandbox_mode(True)
            print("✅ OKX连接初始化完成 (永续合约)")
            
            print("🔄 正在验证交易对...")
            print("ℹ️  注意：首次验证会触发市场数据加载，可能产生一些API请求")
            # 验证交易对是否存在（这会触发市场加载，但我们现在明确知道原因了）
            await self._verify_symbols()
            
        except Exception as e:
            print(f"❌ 交易所初始化失败: {e}")
            raise
    
    def add_callback(self, callback: Callable):
        """添加数据更新回调函数"""
        self.callbacks.append(callback)
    
    async def _verify_symbols(self):
        """验证所需的交易对是否存在并加载合约信息"""
        try:
            # 验证Bybit永续合约symbol
            bybit_markets = await self.exchanges['bybit'].load_markets()
            print(f"📋 Bybit已加载 {len(bybit_markets)} 个交易对")
            
            bybit_symbol = "H/USDT:USDT"  # 硬编码以避免循环依赖
            if bybit_symbol in bybit_markets:
                print(f"✅ Bybit {bybit_symbol} 验证通过")
                # 存储合约信息
                contract_info = bybit_markets[bybit_symbol]
                key = f"bybit_{bybit_symbol}"
                if key not in self.market_data:
                    self.market_data[key] = MarketData(bybit_symbol, "bybit")
                self.market_data[key].contract_info = contract_info
                print(f"📊 Bybit合约信息: 面值={contract_info.get('contractSize', 'N/A')}, "
                      f"最小数量={contract_info.get('limits', {}).get('amount', {}).get('min', 'N/A')}")
            else:
                print(f"❌ Bybit {bybit_symbol} 不存在")
                
            # 验证OKX永续合约symbol  
            okx_markets = await self.exchanges['okx'].load_markets()
            print(f"📋 OKX已加载 {len(okx_markets)} 个交易对")
            
            okx_symbol = "H/USDT:USDT"  # 硬编码以避免循环依赖
            if okx_symbol in okx_markets:
                print(f"✅ OKX {okx_symbol} 验证通过")
                # 存储合约信息
                contract_info = okx_markets[okx_symbol]
                key = f"okx_{okx_symbol}"
                if key not in self.market_data:
                    self.market_data[key] = MarketData(okx_symbol, "okx")
                self.market_data[key].contract_info = contract_info
                print(f"📊 OKX合约信息: 面值={contract_info.get('contractSize', 'N/A')}, "
                      f"最小数量={contract_info.get('limits', {}).get('amount', {}).get('min', 'N/A')}")
            else:
                print(f"❌ OKX {okx_symbol} 不存在")
                # 显示可用的类似交易对
                similar = [s for s in okx_markets.keys() if 'H' in s][:3]
                if similar:
                    print(f"   可用的类似交易对: {similar}")
        except Exception as e:
            print(f"❌ 交易对验证失败: {e}")
    
    async def watch_orderbook(self, exchange_name: str, symbol: str):
        """监控订单簿数据"""
        try:
            exchange = self.exchanges[exchange_name]
            print(f"🔄 开始监控 {exchange_name} - {symbol}")
            
            while self.running:
                orderbook = await exchange.watch_order_book(symbol)
                
                # 更新市场数据
                key = f"{exchange_name}_{symbol}"
                if key not in self.market_data:
                    self.market_data[key] = MarketData(symbol, exchange_name)
                    print(f"📊 首次收到数据: {exchange_name} - {symbol}")
                
                self.market_data[key].orderbook = orderbook
                self.market_data[key].timestamp = time.time()
                
                # 触发回调
                for callback in self.callbacks:
                    try:
                        await callback(self.market_data[key])
                    except Exception as e:
                        print(f"❌ 回调函数执行失败: {e}")
                        
        except Exception as e:
            print(f"❌ 监控{exchange_name}的{symbol}失败: {e}")
            import traceback
            print(f"详细错误: {traceback.format_exc()}")
            # 重连逻辑
            await asyncio.sleep(5)
            if self.running:
                print(f"🔄 尝试重新连接 {exchange_name} - {symbol}")
                await self.watch_orderbook(exchange_name, symbol)
    
    async def start_monitoring(self, symbols_config: list):
        """开始监控指定的交易对"""
        self.running = True
        
        # 创建监控任务
        tasks = []
        for config in symbols_config:
            exchange_name = config['exchange']
            symbol = config['symbol']
            task = asyncio.create_task(
                self.watch_orderbook(exchange_name, symbol)
            )
            tasks.append(task)
        
        print(f"🔄 开始监控 {len(tasks)} 个市场数据流")
        
        # 等待所有任务完成（通常是无限运行）
        await asyncio.gather(*tasks)
    
    async def stop_monitoring(self):
        """停止监控"""
        self.running = False
        
        # 关闭交易所连接
        for exchange in self.exchanges.values():
            await exchange.close()
        
        print("🔌 市场数据监控已停止")
    
    def get_market_data(self, exchange: str, symbol: str) -> Optional[MarketData]:
        """获取指定市场数据"""
        key = f"{exchange}_{symbol}"
        return self.market_data.get(key) 