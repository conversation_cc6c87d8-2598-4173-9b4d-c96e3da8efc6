"""
EMA趋势分析器 - 用于判断价差的方向性，防止滑点
"""
import time
from collections import deque
from typing import Optional, List, Tuple


class EMAIndicator:
    """指数移动平均指标"""
    
    def __init__(self, period: int = 10, alpha: Optional[float] = None):
        """
        初始化EMA指标
        
        Args:
            period: EMA周期
            alpha: 平滑因子，如果None则使用 2/(period+1)
        """
        self.period = period
        self.alpha = alpha if alpha is not None else 2.0 / (period + 1)
        self.ema_value: Optional[float] = None
        self.is_initialized = False
        
    def update(self, value: float) -> float:
        """更新EMA值"""
        if not self.is_initialized:
            self.ema_value = value
            self.is_initialized = True
        else:
            self.ema_value = self.alpha * value + (1 - self.alpha) * self.ema_value
        
        return self.ema_value
    
    def get_value(self) -> Optional[float]:
        """获取当前EMA值"""
        return self.ema_value
    
    def reset(self):
        """重置EMA"""
        self.ema_value = None
        self.is_initialized = False


class SpreadTrendAnalyzer:
    """价差趋势分析器"""
    
    def __init__(self, 
                 ema_period: int = 10,
                 trend_threshold: float = 0.0002,  # 趋势阈值 0.02%
                 min_samples: int = 5,
                 history_limit: int = 50):
        """
        初始化价差趋势分析器
        
        Args:
            ema_period: EMA周期
            trend_threshold: 趋势判断阈值
            min_samples: 最小样本数量
            history_limit: 历史数据保留数量
        """
        self.ema_period = ema_period
        self.trend_threshold = trend_threshold
        self.min_samples = min_samples
        
        # EMA计算器
        self.spread_ema = EMAIndicator(ema_period)
        self.slope_ema = EMAIndicator(max(5, ema_period // 2))  # 斜率EMA周期更短
        
        # 历史数据
        self.spread_history = deque(maxlen=history_limit)
        self.timestamp_history = deque(maxlen=history_limit)
        
        # 趋势状态
        self.last_slope = 0.0
        self.trend_direction: Optional[str] = None  # 'up', 'down', None
        self.trend_strength = 0.0
        
    def update(self, spread: float) -> dict:
        """
        更新价差数据并计算趋势
        
        Args:
            spread: 当前价差值
            
        Returns:
            趋势分析结果
        """
        current_time = time.time()
        
        # 更新历史数据
        self.spread_history.append(spread)
        self.timestamp_history.append(current_time)
        
        # 更新EMA
        ema_value = self.spread_ema.update(spread)
        
        # 计算斜率（趋势方向）
        slope = self._calculate_slope()
        if slope is not None:
            self.last_slope = self.slope_ema.update(slope)
        
        # 判断趋势方向
        self._update_trend_direction()
        
        return self.get_trend_info()
    
    def _calculate_slope(self) -> Optional[float]:
        """计算价差斜率"""
        if len(self.spread_history) < 3:
            return None
            
        # 使用最近几个点计算斜率
        recent_points = min(5, len(self.spread_history))
        if recent_points < 2:
            return None
            
        # 线性回归计算斜率
        spreads = list(self.spread_history)[-recent_points:]
        times = list(self.timestamp_history)[-recent_points:]
        
        if len(set(times)) < 2:  # 时间戳相同
            return 0.0
            
        n = len(spreads)
        sum_x = sum(times)
        sum_y = sum(spreads)
        sum_xy = sum(x * y for x, y in zip(times, spreads))
        sum_x2 = sum(x * x for x in times)
        
        denominator = n * sum_x2 - sum_x * sum_x
        if abs(denominator) < 1e-10:
            return 0.0
            
        slope = (n * sum_xy - sum_x * sum_y) / denominator
        return slope
    
    def _update_trend_direction(self):
        """更新趋势方向判断"""
        if len(self.spread_history) < self.min_samples:
            self.trend_direction = None
            self.trend_strength = 0.0
            return
        
        # 基于EMA斜率判断趋势
        abs_slope = abs(self.last_slope)
        
        if abs_slope > self.trend_threshold:
            if self.last_slope > 0:
                self.trend_direction = 'up'    # 价差扩大趋势
            else:
                self.trend_direction = 'down'  # 价差收窄趋势
            
            # 趋势强度（相对于阈值的倍数）
            self.trend_strength = abs_slope / self.trend_threshold
        else:
            self.trend_direction = None  # 无明确趋势
            self.trend_strength = abs_slope / self.trend_threshold if self.trend_threshold > 0 else 0.0
    
    def get_trend_info(self) -> dict:
        """获取趋势分析信息"""
        return {
            'ema_value': self.spread_ema.get_value(),
            'slope': self.last_slope,
            'trend_direction': self.trend_direction,
            'trend_strength': self.trend_strength,
            'samples_count': len(self.spread_history),
            'is_trending': self.is_trending(),
            'should_open_position': self.should_open_position()
        }
    
    def is_trending(self, min_strength: float = 1.2) -> bool:
        """
        判断是否有明确趋势
        
        Args:
            min_strength: 最小趋势强度
        """
        return (self.trend_direction is not None and 
                self.trend_strength >= min_strength and
                len(self.spread_history) >= self.min_samples)
    
    def should_open_position(self, min_strength: float = 1.5) -> Tuple[bool, Optional[str]]:
        """
        判断是否应该开仓
        
        Args:
            min_strength: 开仓所需的最小趋势强度
            
        Returns:
            (是否应该开仓, 开仓方向)
        """
        if not self.is_trending(min_strength):
            return False, None
        
        # 价差扩大趋势 -> 适合开仓套利（买maker卖taker）
        if self.trend_direction == 'up':
            return True, 'long_spread'  # 做多价差
        # 价差收窄趋势 -> 不适合开仓
        elif self.trend_direction == 'down':
            return False, 'short_spread'  # 可以考虑平仓
        
        return False, None
    
    def reset(self):
        """重置分析器"""
        self.spread_ema.reset()
        self.slope_ema.reset()
        self.spread_history.clear()
        self.timestamp_history.clear()
        self.last_slope = 0.0
        self.trend_direction = None
        self.trend_strength = 0.0
    
    def get_debug_info(self) -> str:
        """获取调试信息"""
        info = self.get_trend_info()
        direction_emoji = {
            'up': '📈',
            'down': '📉',
            None: '➡️'
        }
        
        return (f"EMA趋势: {direction_emoji.get(info['trend_direction'], '➡️')} "
                f"方向={info['trend_direction']} "
                f"强度={info['trend_strength']:.2f} "
                f"斜率={info['slope']:.6f} "
                f"EMA={info['ema_value']:.6f} "
                f"样本={info['samples_count']}") 