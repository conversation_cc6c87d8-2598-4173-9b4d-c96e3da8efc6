"""
订单管理模块 - 处理实际的下单操作
"""
import asyncio
import ccxt.pro as ccxt
import time
from typing import Dict, Optional
from dataclasses import dataclass

@dataclass
class OrderResult:
    """订单结果"""
    success: bool
    order_id: Optional[str] = None
    client_order_id: Optional[str] = None
    message: str = ""
    price: float = 0.0
    amount: float = 0.0

class OrderManager:
    """订单管理器"""
    
    def __init__(self, exchanges_config: Dict[str, Dict]):
        self.exchanges_config = exchanges_config
        self.exchanges: Dict[str, ccxt.Exchange] = {}
        
    async def initialize(self):
        """初始化交易所连接"""
        try:
            # 初始化Bybit
            bybit_config = self.exchanges_config['bybit'].copy()
            bybit_config.update({
                'enableRateLimit': True,
                'options': {
                    'defaultType': 'swap',
                }
            })
            self.exchanges['bybit'] = ccxt.bybit(bybit_config)
            
            # 设置沙盒模式
            if bybit_config.get('sandbox', False):
                self.exchanges['bybit'].set_sandbox_mode(True)
            
            # 初始化OKX
            okx_config = self.exchanges_config['okx'].copy()
            okx_config.update({
                'enableRateLimit': True,
                'options': {
                    'defaultType': 'swap',
                }
            })
            self.exchanges['okx'] = ccxt.okx(okx_config)
            
            # 设置沙盒模式
            if okx_config.get('sandbox', False):
                self.exchanges['okx'].set_sandbox_mode(True)
            
            print("✅ 订单管理器初始化完成")
            
        except Exception as e:
            print(f"❌ 订单管理器初始化失败: {e}")
            raise
    
    async def create_maker_order(self, 
                                exchange: str, 
                                symbol: str, 
                                side: str, 
                                usdt_amount: float,
                                price: float,
                                market_data) -> OrderResult:
        """
        创建maker订单
        
        Args:
            exchange: 交易所名称
            symbol: 交易对
            side: 买卖方向 ('buy' 或 'sell')
            usdt_amount: USDT金额
            price: 下单价格
            market_data: 市场数据对象（包含合约信息）
        """
        try:
            # 获取交易所实例
            if exchange not in self.exchanges:
                return OrderResult(False, message=f"交易所 {exchange} 未初始化")
            
            exchange_instance = self.exchanges[exchange]
            
            # 计算合约张数
            if market_data and hasattr(market_data, 'get_contract_amount'):
                amount = market_data.get_contract_amount(usdt_amount)
            else:
                # 如果没有合约信息，使用USDT金额除以价格
                amount = usdt_amount / price if price > 0 else usdt_amount
            
            # 生成自定义订单号
            timestamp = int(time.time() * 1000)
            client_order_id = f"TEST_{timestamp}"
            
            print(f"📝 准备下单:")
            print(f"   交易所: {exchange}")
            print(f"   交易对: {symbol}")
            print(f"   方向: {side}")
            print(f"   价格: {price}")
            print(f"   数量: {amount}")
            print(f"   订单号: {client_order_id}")
            
            # 根据不同交易所设置不同的参数
            order_params = {
                'clientOrderId': client_order_id,
            }
            
            # Bybit特定参数
            if exchange == 'bybit':
                order_params['position_idx'] = 0  # 单向持仓模式
            # OKX特定参数
            elif exchange == 'okx':
                order_params['tdMode'] = 'cross'  # 全仓模式
                order_params['posSide'] = 'long' if side == 'buy' else 'short'
            
            order = await exchange_instance.create_order(
                symbol=symbol,
                type='limit',
                side=side,
                amount=amount,
                price=price,
                params=order_params
            )
            
            print(f"✅ 下单成功!")
            print(f"   订单ID: {order.get('id', 'N/A')}")
            print(f"   状态: {order.get('status', 'N/A')}")
            
            return OrderResult(
                success=True,
                order_id=order.get('id'),
                client_order_id=client_order_id,
                message="下单成功",
                price=price,
                amount=amount
            )
            
        except Exception as e:
            error_msg = f"下单失败: {str(e)}"
            print(f"❌ {error_msg}")
            return OrderResult(False, message=error_msg)
    
    async def get_order_status(self, exchange: str, order_id: str) -> Optional[Dict]:
        """
        获取订单状态
        
        Args:
            exchange: 交易所名称
            order_id: 订单ID
            
        Returns:
            订单状态信息
        """
        try:
            if exchange not in self.exchanges:
                return None
                
            exchange_instance = self.exchanges[exchange]
            order = await exchange_instance.fetch_order(order_id)
            
            return {
                'id': order.get('id'),
                'status': order.get('status'),
                'filled': order.get('filled', 0),
                'remaining': order.get('remaining', 0),
                'amount': order.get('amount', 0),
                'price': order.get('price', 0),
                'side': order.get('side'),
                'type': order.get('type')
            }
            
        except Exception as e:
            print(f"❌ 获取订单状态失败: {e}")
            return None
    
    async def cancel_order(self, exchange: str, order_id: str) -> bool:
        """
        取消订单
        
        Args:
            exchange: 交易所名称
            order_id: 订单ID
            
        Returns:
            是否成功取消
        """
        try:
            if exchange not in self.exchanges:
                return False
                
            exchange_instance = self.exchanges[exchange]
            result = await exchange_instance.cancel_order(order_id)
            
            print(f"✅ 订单取消成功: {order_id}")
            return True
            
        except Exception as e:
            print(f"❌ 取消订单失败: {e}")
            return False
    
    async def close(self):
        """关闭连接"""
        for exchange in self.exchanges.values():
            await exchange.close()
        print("🔌 订单管理器连接已关闭") 