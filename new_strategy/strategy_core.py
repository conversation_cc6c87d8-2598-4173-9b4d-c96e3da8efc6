"""
策略核心逻辑模块 - 实现V2版本价差检查（参考旧策略的持仓方向判断）
"""
import asyncio
import time
import sys
from typing import Dict, List, Optional
from dataclasses import dataclass, field
from new_strategy.market_data import MarketData, MarketDataManager
from new_strategy.order_manager import OrderManager

@dataclass
class PositionInfo:
    """持仓信息"""
    symbol: str
    exchange: str  
    size: float = 0
    timestamp: float = 0

@dataclass  
class SpreadCheck:
    """价差检查结果"""
    spread_open: float = 0
    spread_close: float = 0
    timestamp: float = 0
    valid: bool = False

class CrossMarketStrategy:
    """跨市场套利策略核心"""
    
    def __init__(self, config, market_manager: MarketDataManager, strategy_manager=None):
        self.config = config
        self.market_manager = market_manager
        self.strategy_manager = strategy_manager  # 添加策略管理器引用
        
        # 策略状态
        self.maker_position = 0
        self.taker_position = 0
        self.loop_times = 0
        
        # 价差检查相关
        self.spread_checks: List[SpreadCheck] = []
        self.last_check_time = 0
        
        # 最新市场数据
        self.maker_data: Optional[MarketData] = None
        self.taker_data: Optional[MarketData] = None
        
        # 订单管理器
        self.order_manager = OrderManager(config.exchanges_config)
        
        # 订单监控状态
        self.maker_order_pending = False  # maker订单是否待成交
        self.maker_order_id = None  # maker订单ID
        self.maker_order_amount = 0  # maker订单数量
        self.maker_order_price = 0  # maker订单价格
        
        # 注册数据回调
        market_manager.add_callback(self.on_market_data_update)
    
    async def initialize(self):
        """初始化策略"""
        await self.order_manager.initialize()
    
    async def on_market_data_update(self, market_data: MarketData):
        """市场数据更新回调"""
        if market_data.exchange == self.config.maker_exchange:
            self.maker_data = market_data
        elif market_data.exchange == self.config.taker_exchange:
            self.taker_data = market_data
        
        # 检查是否可以进行策略判断
        await self.check_trading_opportunity()
    
    def check_balance(self) -> bool:
        """检查持仓平衡性（风险控制）"""
        if self.maker_position == 0 and self.taker_position == 0:
            return True
            
        mid = (abs(self.maker_position) + abs(self.taker_position)) / 2.0
        if mid < self.config.eps:
            return True
            
        inventory_ratio = abs(
            (self.maker_position + self.taker_position) / (mid + self.config.eps)
        )
        
        result = inventory_ratio <= self.config.inventory_risk_ratio
        
        if not result:
            print(f"⚠️ 持仓风险过高 inventory_ratio={inventory_ratio:.4f}")
        else:
            # 调试信息，正常运行时不显示
            pass
            
        return result
    
    def check_position_limit(self) -> bool:
        """检查持仓限制"""
        future_position = self.maker_position + self.config.single_maker_amt
        result = future_position < self.config.max_position + self.config.eps
        
        if not result:
            print(f"持仓限制检查失败 当前:{self.maker_position} 限制:{self.config.max_position}")
        
        return result
    
    def check_close_position_limit(self) -> bool:
        """检查平仓持仓限制"""
        future_position = self.maker_position - self.config.single_maker_amt
        result = future_position > -(self.config.max_position + self.config.eps)
        
        if not result:
            print(f"平仓持仓限制检查失败 当前:{self.maker_position} 限制:{-self.config.max_position}")
        
        return result
    
    def calculate_spread(self) -> Optional[SpreadCheck]:
        """计算价差"""
        if not self.maker_data or not self.taker_data:
            print("❌ 缺少市场数据")
            return None
            
        if not self.maker_data.orderbook or not self.taker_data.orderbook:
            print("❌ 订单簿数据为空")
            return None
            
        try:
            # 获取最佳买卖价
            maker_bid = self.maker_data.get_best_bid()
            maker_ask = self.maker_data.get_best_ask()
            taker_bid = self.taker_data.get_best_bid()
            taker_ask = self.taker_data.get_best_ask()
            
            if not all([maker_bid, maker_ask, taker_bid, taker_ask]):
                print(f"❌ 价格数据无效: maker_bid={maker_bid}, maker_ask={maker_ask}, taker_bid={taker_bid}, taker_ask={taker_ask}")
                return None
            
            # 计算开仓和平仓价差（参考旧策略）
            spread_open = taker_bid / maker_bid - 1  # 开仓价差
            spread_close = maker_ask / taker_ask - 1  # 平仓价差
            
            return SpreadCheck(
                spread_open=spread_open,
                spread_close=spread_close, 
                timestamp=time.time(),
                valid=True
            )
            
        except Exception as e:
            print(f"❌ 计算价差失败: {e}")
            return None
    
    async def perform_spread_checks(self) -> Dict[str, int]:
        """执行V2版本的多次价差检查"""
        spread_open_counter = 0
        spread_close_counter = 0
        
        print(f"📊 开始进行{self.config.spread_check_times}次价差检查")
        
        for i in range(self.config.spread_check_times):
            if i > 0:  # 第一次不需要等待
                await asyncio.sleep(self.config.spread_check_delay)
            
            spread_check = self.calculate_spread()
            if not spread_check or not spread_check.valid:
                print(f"⚠️ 价差检查[{i+1}/{self.config.spread_check_times}] 数据无效")
                continue
            
            print(
                f"📈 价差检查[{i+1}/{self.config.spread_check_times}] "
                f"开仓价差:{spread_check.spread_open:.5f} "
                f"平仓价差:{spread_check.spread_close:.5f}"
            )
            
            # 检查开仓条件
            if spread_check.spread_open > self.config.spread_limit:
                spread_open_counter += 1
                print(f"开仓价差满足条件 计数:{spread_open_counter}")
            
            # 检查平仓条件
            if spread_check.spread_close > self.config.spread_limit:
                spread_close_counter += 1  
                print(f"平仓价差满足条件 计数:{spread_close_counter}")
        
        return {
            'open_counter': spread_open_counter,
            'close_counter': spread_close_counter
        }
    
    async def check_trading_opportunity(self):
        """检查交易机会"""
        # 防止过于频繁的检查
        current_time = time.time()
        if current_time - self.last_check_time < self.config.update_interval:
            return
        
        self.last_check_time = current_time
        self.loop_times += 1
        
        print(f"策略循环 #{self.loop_times}")
        
        # 1. 检查maker订单状态（如果有待成交订单）
        if self.maker_order_pending and self.maker_order_id:
            await self.check_maker_order_status()
        
        # 2. 基础条件检查（模拟策略中注释掉，专注于观察交易机会）
        # if not self.check_balance():
        #     print("风险检查未通过，跳过此次机会")
        #     return
        
        # 3. 执行价差检查
        spread_results = await self.perform_spread_checks()
        
        # 4. 判断是否开仓（参考旧策略的持仓方向判断）
        if (spread_results['open_counter'] == self.config.spread_check_times and 
            self.check_position_limit() and not self.maker_order_pending):
            print(f"🎯 开仓条件满足: 连续{self.config.spread_check_times}次价差检查通过 + 持仓限制检查通过")
            await self.execute_open_position()
        
        # 5. 判断是否平仓（参考旧策略的持仓方向判断）
        if (spread_results['close_counter'] == self.config.spread_check_times and 
            self.check_close_position_limit()):
            print(f"🎯 平仓条件满足: 连续{self.config.spread_check_times}次价差检查通过 + 持仓限制检查通过")
            await self.simulate_close_position()
    
    async def check_maker_order_status(self):
        """检查maker订单状态"""
        try:
            order_status = await self.order_manager.get_order_status(
                self.config.maker_exchange, 
                self.maker_order_id
            )
            
            if not order_status:
                print(f"⚠️ 无法获取订单状态: {self.maker_order_id}")
                return
            
            status = order_status.get('status', 'unknown')
            filled = order_status.get('filled', 0)
            remaining = order_status.get('remaining', 0)
            
            print(f"📊 Maker订单状态检查:")
            print(f"   订单ID: {self.maker_order_id}")
            print(f"   状态: {status}")
            print(f"   已成交: {filled}")
            print(f"   剩余: {remaining}")
            
            # 如果订单已完全成交
            if status == 'closed' and filled > 0:
                print("✅ Maker订单已完全成交，准备在Taker市场下单对冲...")
                await self.execute_taker_hedge(filled)
                
            # 如果订单部分成交
            elif status == 'open' and filled > 0:
                print(f"⚠️ Maker订单部分成交: {filled}/{self.maker_order_amount}")
                
            # 如果订单被取消
            elif status == 'canceled':
                print("❌ Maker订单被取消")
                self.maker_order_pending = False
                self.maker_order_id = None
                
        except Exception as e:
            print(f"❌ 检查订单状态失败: {e}")
    
    async def execute_taker_hedge(self, maker_filled_amount: float):
        """在taker市场执行对冲订单"""
        if not self.taker_data:
            print("❌ 缺少taker市场数据")
            return
            
        try:
            # 计算对冲数量（考虑合约差异）
            hedge_amount = self.calculate_hedge_amount(maker_filled_amount)
            
            # 获取taker市场价格
            taker_price = self.taker_data.get_best_ask()  # 使用taker卖价进行对冲
            
            print("=" * 60)
            print("🔄 执行Taker对冲订单")
            print(f"📊 Maker成交数量: {maker_filled_amount}")
            print(f"📊 Taker对冲数量: {hedge_amount}")
            print(f"💰 Taker对冲价格: {taker_price:.6f}")
            print(f"🏪 Taker市场({self.config.taker_exchange}): {self.config.taker_symbol}")
            print("=" * 60)
            
            # 执行taker对冲订单
            result = await self.order_manager.create_maker_order(
                exchange=self.config.taker_exchange,
                symbol=self.config.taker_symbol,
                side='sell',  # 对冲：maker买，taker卖
                usdt_amount=hedge_amount,
                price=taker_price,
                market_data=self.taker_data
            )
            
            if result.success:
                print("✅ Taker对冲订单下单成功!")
                print("🎯 策略执行完成，程序即将退出...")
                # 清理maker订单状态
                self.maker_order_pending = False
                self.maker_order_id = None
                # 优雅退出
                await self.cleanup()
                if self.strategy_manager:
                    self.strategy_manager.force_stop()
                asyncio.get_event_loop().stop()
            else:
                print(f"❌ Taker对冲订单失败: {result.message}")
                
        except Exception as e:
            print(f"❌ 执行Taker对冲失败: {e}")
    
    def calculate_hedge_amount(self, maker_amount: float) -> float:
        """计算对冲数量，考虑合约差异"""
        # 获取两个市场的合约信息
        maker_contract_info = self.maker_data.contract_info if self.maker_data else None
        taker_contract_info = self.taker_data.contract_info if self.taker_data else None
        
        # 计算maker市场的USDT价值
        maker_usdt_value = maker_amount * self.maker_order_price
        
        # 如果taker市场有合约信息，按合约面值计算
        if taker_contract_info:
            taker_contract_size = taker_contract_info.get('contractSize', 1.0)
            if taker_contract_size > 0:
                # 按USDT价值计算taker合约数量
                taker_amount = maker_usdt_value / taker_contract_size
                # 确保不小于最小数量
                min_amount = taker_contract_info.get('limits', {}).get('amount', {}).get('min', 0.1)
                if taker_amount < min_amount:
                    taker_amount = min_amount
                return taker_amount
        
        # 如果没有合约信息，使用配置的scale
        return maker_usdt_value * self.config.taker_scale
    
    async def execute_open_position(self):
        """执行开仓操作（实际下单）"""
        if not self.maker_data or not self.taker_data:
            return
            
        maker_price = self.maker_data.get_best_bid()  # 使用maker买价
        spread_check = self.calculate_spread()
        
        if not spread_check:
            return
            
        print("=" * 60)
        print("🚀 开仓信号触发!")
        print(f"📊 循环次数: {self.loop_times}")
        print(f"💰 开仓价差: {spread_check.spread_open:.5f} ({spread_check.spread_open*100:.3f}%)")
        print(f"🏦 Maker市场({self.config.maker_exchange}): {self.config.maker_symbol}")
        print(f"   📈 挂买单价格: {maker_price:.6f}")
        print(f"   📦 挂单数量: {self.config.single_maker_amt}")
        print(f"🏪 Taker市场({self.config.taker_exchange}): {self.config.taker_symbol}")
        print(f"   📊 对冲数量: {self.config.single_maker_amt * self.config.taker_scale}")
        print(f"⚖️ 当前持仓 - Maker: {self.maker_position}, Taker: {self.taker_position}")
        print(f"⏰ 时间: {time.strftime('%H:%M:%S')}")
        print("=" * 60)
        
        # 执行实际下单
        usdt_amount = self.config.single_maker_amt
        result = await self.order_manager.create_maker_order(
            exchange=self.config.maker_exchange,
            symbol=self.config.maker_symbol,
            side='buy',
            usdt_amount=usdt_amount,
            price=maker_price,
            market_data=self.maker_data
        )
        
        if result.success:
            print("✅ Maker订单下单成功，开始监控订单状态...")
            # 记录订单信息用于监控
            self.maker_order_pending = True
            self.maker_order_id = result.order_id
            self.maker_order_amount = result.amount
            self.maker_order_price = result.price
            # 更新模拟持仓（做多maker）
            self.maker_position += self.config.single_maker_amt
            print(f"📊 订单监控信息:")
            print(f"   订单ID: {self.maker_order_id}")
            print(f"   订单数量: {self.maker_order_amount}")
            print(f"   订单价格: {self.maker_order_price}")
        else:
            print(f"❌ 下单失败: {result.message}")
        
    async def simulate_close_position(self):
        """模拟平仓操作（在maker市场挂卖单）"""
        if not self.maker_data or not self.taker_data:
            return
            
        maker_price = self.maker_data.get_best_ask()  # 使用maker卖价
        spread_check = self.calculate_spread()
        
        if not spread_check:
            return
            
        print("=" * 60) 
        print("📉 平仓信号触发!")
        print(f"📊 循环次数: {self.loop_times}")
        print(f"💰 平仓价差: {spread_check.spread_close:.5f} ({spread_check.spread_close*100:.3f}%)")
        print(f"🏦 Maker市场({self.config.maker_exchange}): {self.config.maker_symbol}")
        print(f"   📈 挂卖单价格: {maker_price:.6f}")
        print(f"   📦 挂单数量: {self.config.single_maker_amt}")
        print(f"🏪 Taker市场({self.config.taker_exchange}): {self.config.taker_symbol}")
        print(f"   📊 对冲数量: {self.config.single_maker_amt * self.config.taker_scale}")
        print(f"⚖️ 当前持仓 - Maker: {self.maker_position}, Taker: {self.taker_position}")
        print(f"⏰ 时间: {time.strftime('%H:%M:%S')}")
        print("=" * 60)
        
        # 更新模拟持仓（做空maker）
        self.maker_position -= self.config.single_maker_amt 
    
    async def cleanup(self):
        """清理资源"""
        await self.order_manager.close()
        await self.market_manager.stop_monitoring() 