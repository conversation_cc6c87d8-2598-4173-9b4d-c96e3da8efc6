"""
新版本套利策略配置文件 - 基于ccxtpro和协程
"""
from dataclasses import dataclass
from typing import Dict, Any

@dataclass
class TradingConfig:
    """交易配置类"""
    
    # 交易所配置
    maker_exchange: str = "bybit"
    taker_exchange: str = "okx"  # 注意：ccxtpro中OKEx改名为okx
    maker_symbol: str = "H/USDT:USDT"   # 🔥 Bybit永续合约格式
    taker_symbol: str = "H/USDT:USDT"   # 🔥 OKX永续合约格式
    
    # 交易量配置
    single_maker_amt: float = 10.0  # USDT金额
    taker_scale: float = 10.0
    max_position: float = 100.0
    scale: float = 1.0
    
    # 价差控制参数（V2版本）
    spread_limit: float = 0.0005  # 最小开仓价差 0.05%
    spread_check_times: int = 3   # 价差检查次数
    spread_check_delay: float = 0.5  # 检查间隔（秒）
    maker_spread: float = 0.003   # 挂单价差 0.3%
    maker_depth: int = 1          # 订单簿深度层级
    
    # 风险控制参数
    inventory_risk_ratio: float = 0.01  # 库存风险比例 1%
    taker_cancel_margin: float = 0.0015  # 取消订单价差阈值 0.15%
    limit_cancel_time: float = 1.0       # 取消订单最小间隔
    eps: float = 1e-3                    # 数值精度
    
    # EMA趋势分析参数
    ema_period: int = 10                 # EMA周期
    trend_threshold: float = 0.0002      # 趋势判断阈值 0.02%
    min_trend_strength: float = 1.5      # 开仓所需最小趋势强度
    min_trend_samples: int = 5           # 趋势分析最小样本数
    
    # 系统配置
    update_interval: float = 2.0     # 策略循环间隔
    debug_mode: bool = False         # 调试模式（True=详细日志，False=简洁日志）
    simulate_only: bool = True       # 仅模拟交易，不实际下单
    
    # 交易所API配置（示例，实际使用时需要填入真实API）
    exchanges_config: Dict[str, Dict[str, Any]] = None
    
    def __post_init__(self):
        if self.exchanges_config is None:
            self.exchanges_config = {
                "bybit": {
                    "apiKey": "HSxpaVXSyLzUPDDyxi",  # 请填入真实API Key
                    "secret": "phiDVjq05GHRa5yNW4viC15zSBdysZ1zvMqA",   # 请填入真实Secret
                    "sandbox": False,  # Bybit测试环境
                },
                "okx": {
                    "apiKey": "80f81c2c-3362-4ddf-bc5f-e88c5ee3fef2",        # 请填入真实API Key
                    "secret": "CE4DC9297D8D19554EFE7B2782C4DBA6",         # 请填入真实Secret  
                    "password": "Yjy10qpalzm,",   # 请填入真实Passphrase
                    "sandbox": False,  # OKX生产环境（不支持sandbox）
                }
            }

# 全局配置实例
config = TradingConfig() 