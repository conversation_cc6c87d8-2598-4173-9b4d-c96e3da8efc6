"""
交易规则监控器 - 负责轮询获取和管理交易所的交易规则信息
"""
import asyncio
import time
import ccxt.pro as ccxt
from typing import Dict, Any, Optional, Callable


class TradingRulesMonitor:
    """交易规则监控器"""
    
    def __init__(self, maker_exchange: ccxt.Exchange, taker_exchange: ccxt.Exchange):
        self.maker_exchange = maker_exchange
        self.taker_exchange = taker_exchange
        self.trading_rules: Dict[str, Any] = {}
        self.polling_task: Optional[asyncio.Task] = None
        self.is_polling = False
        self.callbacks: list = []  # 更新回调函数列表
    
    def add_update_callback(self, callback: Callable[[Dict[str, Any]], None]):
        """
        添加交易规则更新时的回调函数
        
        Args:
            callback: 回调函数，参数为更新后的交易规则字典
        """
        self.callbacks.append(callback)
    
    async def initialize(self):
        """初始化监控器，加载初始交易规则"""
        print("🔄 初始化交易规则监控器...")
        await self.update_trading_rules()
        print("✅ 交易规则监控器初始化完成")
    
    async def start_polling(self, interval: float = 5.0):
        """
        启动交易规则轮询
        
        Args:
            interval: 轮询间隔（秒），默认5秒
        """
        if self.is_polling:
            print("⚠️ 交易规则轮询已在运行中")
            return
            
        self.is_polling = True
        print(f"🔄 启动交易规则轮询，间隔: {interval}秒")
        
        self.polling_task = asyncio.create_task(self._polling_loop(interval))
    
    async def stop_polling(self):
        """停止交易规则轮询"""
        if not self.is_polling:
            return
            
        self.is_polling = False
        if self.polling_task:
            self.polling_task.cancel()
            try:
                await self.polling_task
            except asyncio.CancelledError:
                pass
        print("🛑 交易规则轮询已停止")
    
    async def _polling_loop(self, interval: float):
        """轮询循环"""
        while self.is_polling:
            try:
                await self.update_trading_rules()
                await asyncio.sleep(interval)
            except asyncio.CancelledError:
                break
            except Exception as e:
                print(f"❌ 轮询交易规则时发生错误: {e}")
                await asyncio.sleep(interval)
    
    async def update_trading_rules(self):
        """更新交易规则信息"""
        try:
            print(f"📋 正在更新交易规则... {time.strftime('%H:%M:%S')}")
            
            # 获取maker交易所的市场信息
            maker_markets = await self.maker_exchange.load_markets()
            maker_name = self.maker_exchange.id
            
            # 获取taker交易所的市场信息  
            taker_markets = await self.taker_exchange.load_markets()
            taker_name = self.taker_exchange.id
            
            # 保存交易规则
            previous_timestamp = self.trading_rules.get('timestamp', 0)
            
            self.trading_rules = {
                'timestamp': time.time(),
                'maker': {
                    'exchange': maker_name,
                    'markets_count': len(maker_markets),
                    'markets': maker_markets
                },
                'taker': {
                    'exchange': taker_name, 
                    'markets_count': len(taker_markets),
                    'markets': taker_markets
                }
            }
            
            print(f"✅ 交易规则更新完成 - Maker({maker_name}): {len(maker_markets)}个交易对, "
                  f"Taker({taker_name}): {len(taker_markets)}个交易对")
            
            # 触发回调函数（只有非首次更新才触发）
            if previous_timestamp > 0:
                for callback in self.callbacks:
                    try:
                        callback(self.trading_rules)
                    except Exception as e:
                        print(f"❌ 执行更新回调时出错: {e}")
            
        except Exception as e:
            print(f"❌ 更新交易规则失败: {e}")
    
    def get_trading_rules(self, exchange: str = None) -> Dict[str, Any]:
        """
        获取交易规则
        
        Args:
            exchange: 指定交易所名称('maker' 或 'taker')，None表示获取全部
            
        Returns:
            交易规则字典
        """
        if exchange and exchange in self.trading_rules:
            return self.trading_rules[exchange]
        return self.trading_rules
    
    def get_symbol_info(self, symbol: str, exchange: str) -> Optional[Dict[str, Any]]:
        """
        获取特定交易对的信息
        
        Args:
            symbol: 交易对符号
            exchange: 交易所('maker' 或 'taker')
            
        Returns:
            交易对信息字典，如果不存在返回None
        """
        if exchange not in self.trading_rules or 'markets' not in self.trading_rules[exchange]:
            return None
            
        markets = self.trading_rules[exchange]['markets']
        return markets.get(symbol)
    
    def get_symbol_limits(self, symbol: str, exchange: str) -> Optional[Dict[str, Any]]:
        """
        获取交易对的限制信息（最小/最大数量、价格等）
        
        Args:
            symbol: 交易对符号
            exchange: 交易所('maker' 或 'taker')
            
        Returns:
            限制信息字典
        """
        symbol_info = self.get_symbol_info(symbol, exchange)
        if symbol_info:
            return symbol_info.get('limits', {})
        return None
    
    def get_trading_fees(self, symbol: str, exchange: str) -> Optional[Dict[str, Any]]:
        """
        获取交易对的手续费信息
        
        Args:
            symbol: 交易对符号
            exchange: 交易所('maker' 或 'taker')
            
        Returns:
            手续费信息字典
        """
        symbol_info = self.get_symbol_info(symbol, exchange)
        if symbol_info:
            return {
                'maker': symbol_info.get('maker', 0),
                'taker': symbol_info.get('taker', 0)
            }
        return None
    
    def print_trading_rules_summary(self):
        """打印交易规则摘要"""
        if not self.trading_rules:
            print("❌ 暂无交易规则数据")
            return
            
        print("=" * 60)
        print("📊 交易规则摘要")
        print(f"🕒 更新时间: {time.strftime('%Y-%m-%d %H:%M:%S', time.localtime(self.trading_rules['timestamp']))}")
        
        for exchange_type in ['maker', 'taker']:
            if exchange_type in self.trading_rules:
                info = self.trading_rules[exchange_type]
                print(f"🏦 {exchange_type.upper()}交易所({info['exchange']}): {info['markets_count']}个交易对")
        print("=" * 60)
    
    def print_symbol_details(self, symbol: str):
        """
        打印特定交易对在两个交易所的详细信息
        
        Args:
            symbol: 交易对符号
        """
        print(f"📊 {symbol} 详细信息:")
        print("-" * 40)
        
        for exchange_type in ['maker', 'taker']:
            symbol_info = self.get_symbol_info(symbol, exchange_type)
            if symbol_info:
                exchange_name = self.trading_rules[exchange_type]['exchange']
                print(f"🏦 {exchange_name.upper()}:")
                
                # 基本信息
                print(f"   激活状态: {'✅' if symbol_info.get('active', False) else '❌'}")
                print(f"   合约大小: {symbol_info.get('contractSize', 'N/A')}")
                
                # 限制信息
                limits = symbol_info.get('limits', {})
                if limits:
                    amount_limits = limits.get('amount', {})
                    price_limits = limits.get('price', {})
                    cost_limits = limits.get('cost', {})
                    
                    print(f"   数量限制: {amount_limits.get('min', 'N/A')} - {amount_limits.get('max', 'N/A')}")
                    print(f"   价格限制: {price_limits.get('min', 'N/A')} - {price_limits.get('max', 'N/A')}")
                    print(f"   成本限制: {cost_limits.get('min', 'N/A')} - {cost_limits.get('max', 'N/A')}")
                
                # 手续费
                print(f"   Maker费率: {symbol_info.get('maker', 'N/A')}")
                print(f"   Taker费率: {symbol_info.get('taker', 'N/A')}")
                print()
            else:
                exchange_name = self.trading_rules[exchange_type]['exchange']
                print(f"🏦 {exchange_name.upper()}: ❌ 不支持此交易对")
                print()
    
    def is_symbol_available(self, symbol: str) -> Dict[str, bool]:
        """
        检查交易对在两个交易所的可用性
        
        Args:
            symbol: 交易对符号
            
        Returns:
            可用性字典 {'maker': bool, 'taker': bool}
        """
        return {
            'maker': self.get_symbol_info(symbol, 'maker') is not None,
            'taker': self.get_symbol_info(symbol, 'taker') is not None
        }
    
    async def cleanup(self):
        """清理资源"""
        await self.stop_polling()
        self.callbacks.clear()
        print("🧹 交易规则监控器已清理") 