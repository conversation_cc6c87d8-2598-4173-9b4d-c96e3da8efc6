import os
import config
from bybit_ws import BybitWebsocket
from log import logger
import threading
import ccxt
import time
from threading import Timer
from easydict import EasyDict
from config import config_bybit
import traceback


class Bybit:
    def __init__(self):
        self.exchange = ccxt.bybit(
            {'apiKey': config_bybit.api_key, 'secret': config_bybit.api_secret, 'enableRateLimit': True})
        self.exchange.load_markets()
            #wsURL=config_bybit.bybit_ws_url,
        self.bybit_ws = BybitWebsocket(
            wsURL="wss://stream.bybit.com/realtime_public",
            api_key=config_bybit.api_key,
            api_secret=config_bybit.api_secret,
            on_msg=self.on_msg)
      
        self.bybit_ws.subscribe_trade("LUNAUSDT")
        self.on_finish_func_lists = {}
        self.orders_status = {}

    def on_msg(self):
        logger.warning("on msg execution")
#        data = self.bybit_ws.get_data("execution")
#        logger.info(data)
#        logger.info(data)
#        client_id = data['data'][0]['order_link_id']
#        logger.error(self.on_finish_func_lists.keys())
#        logger.error(client_id)
#        if client_id != "" and client_id in self.on_finish_func_lists.keys():
#            logger.error("callback")
#            func = self.on_finish_func_lists[client_id]
#            thread = threading.Thread(target=func, args=(data.copy(),))
#            thread.start()
#            if client_id in self.orders_status.keys():
#                self.orders_status[client_id] = True


if __name__ == "__main__":
    bybit = Bybit()
