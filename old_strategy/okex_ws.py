import ssl

import websocket
import threading
import traceback
from time import sleep
import json
import logging
import urllib
import math
import time
import hmac
from log import logger
import base64



# This is a simple adapters for connecting to Bybit's websocket API.
# You could use methods whose names begin with “subscribe”, and get result by "get_data" method.
# All the provided websocket APIs are implemented, includes public and private topic.
# Private methods are only available after authorization, but public methods do not.
# If you have any questions during use, please contact us vim the e-mail
# "<EMAIL>".

def get_timestamp():
    now = datetime.datetime.now()
    t = now.isoformat("T", "milliseconds")
    return t + "Z"

def get_local_timestamp():
      return int(time.time())


def login_params(timestamp, api_key, passphrase, secret_key):
    message = timestamp + 'GET' + '/users/self/verify'

    mac = hmac.new(bytes(secret_key, encoding='utf8'), bytes(message, encoding='utf-8'), digestmod='sha256')
    d = mac.digest()
    sign = base64.b64encode(d)

    login_param = {"op": "login", "args": [{"apiKey": api_key,
                                            "passphrase": passphrase,
                                            "timestamp": timestamp,
                                            "sign": sign.decode("utf-8")}]}
    login_str = json.dumps(login_param)
    return login_str



class OkexWebsocket:

    def __init__(self, wsURL, api_key,passphrase , api_secret, on_msg):
        '''Initialize'''
        self.logger = logger
        self.logger.debug("Initializing WebSocket.")

        if api_key is not None and api_secret is None:
            raise ValueError('api_secret is required if api_key is provided')
        if api_key is None and api_secret is not None:
            raise ValueError('api_key is required if api_secret is provided')

        self.api_key = api_key
        self.api_secret = api_secret
        self.passphrase = passphrase

        self.data = {}
        self.exited = False
        self.auth = False
        self.logger.info("Connecting to %s" % wsURL)
        self.on_msg = on_msg
        #self.on_msg({"test":"test"})
        self.__connect(wsURL)


    def exit(self):
        '''Call this to exit - will close websocket.'''
        self.exited = True
        self.ws.close()

    def __connect(self, wsURL):
        '''Connect to the websocket in a thread.'''
        self.logger.debug("Starting thread")

        #self.ws = websocket.WebSocketApp(wsURL,
        #                                 on_message=self.__on_message,
        #                                 on_close=self.__on_close,
        #                                 on_open=self.__on_open,
        #                                 on_error=self.__on_error,
        #                                 on_ping=self.__on_ping,
        #                                 on_pong=self.__on_pong,
        #                                 keep_running=True)

        self.ws = websocket.WebSocketApp(wsURL,
                                         on_message=self.__on_message,
                                         on_close=self.__on_close,
                                         on_open=self.__on_open,
                                         on_error=self.__on_error,
                                         on_ping=self.__on_ping,
                                         on_pong=self.__on_pong,
                                         keep_running=True)

        self.wst = threading.Thread(
            target=lambda: self.ws.run_forever(
                ping_interval=5, ping_timeout=4))

        #self.wst = threading.Thread(
        #    target=lambda: self.ws.run_forever(
        #        ping_interval=30, ping_timeout=10))
        #self.wst.daemon = True
        self.wst.start()
        self.logger.debug("Started thread")

        # Wait for connect before continuing
        retry_times = 5
        while not self.ws.sock or not self.ws.sock.connected and retry_times:
            sleep(1)
            retry_times -= 1
        if retry_times == 0 and not self.ws.sock.connected:
            self.logger.error("Couldn't connect to WebSocket! Exiting.")
            self.exit()
            raise websocket.WebSocketTimeoutException(
                'Error！Couldn not connect to WebSocket!.')

        if self.api_key and self.api_secret:
            self.__do_auth()

    def __on_ping(self):
        self.logger.info("send ping")
        self.ws.send("ping")
        pass

    def __on_pong(self):
        self.logger.info("send pong")
        self.ws.send("pong")
        pass

    def generate_signature(self, expires):
        """Generate a request signature."""
        _val = 'GET/realtime' + expires
        return str(
            hmac.new(
                bytes(
                    self.api_secret,
                    "utf-8"),
                bytes(
                    _val,
                    "utf-8"),
                digestmod="sha256").hexdigest())

    def __do_auth(self):
        timestamp = str(get_local_timestamp())
        args = login_params(timestamp , self.api_key , self.passphrase , self.api_secret)
        self.ws.send(args)
        self.logger.info("do auth")

    def __on_message(self, message):
        '''Handler for parsing WS messages.'''
        message = json.loads(message)
        if 'event' in message.keys() and message['code'] == "0":
            self.logger.info("ping")
            self.ping()
        #self.logger.debug(message)
        if self.on_msg:
            #self.logger.info("on msg")
            #self.on_msg({'test':'test'})
            self.on_msg(message)
#        if 'success' in message and message["success"]:
#            if 'request' in message and message["request"]["op"] == 'auth':
#                self.auth = True
#                self.logger.info("Authentication success.")
#            if 'ret_msg' in message and message["ret_msg"] == 'pong':
#                self.data["pong"].append("PING success")
#
#        if 'topic' in message:
#            self.data[message["topic"]].append(message)
#            if len(self.data[message["topic"]]
#                   ) > BybitWebsocket.MAX_DATA_CAPACITY:
#                self.data[message["topic"]] = self.data[message["topic"]
#                                                        ][BybitWebsocket.MAX_DATA_CAPACITY // 2:]
#        self.on_msg()

    def __on_error(self, error):
        '''Called on fatal websocket errors. We exit on these.'''
        if not self.exited:
            self.logger.error("Error : %s" % error)
            raise websocket.WebSocketException(error)

    def __on_open(self):
        '''Called when the WS opens.'''
        self.logger.debug("Websocket Opened.")

    def __on_close(self):
        '''Called on websocket close.'''
        self.logger.info('Websocket Closed')

    def ping(self):
        self.ws.send("ping")

    def test_trade(self):
        data = { "id": "TADEBUG", "op": "order", "args": [{ "side": "buy", "instId": "LUNA-USDT-SWAP", "tdMode": "cross", "ordType": "market", "sz": "1" }] }
        data_str = json.dumps(data)
        self.ws.send(data_str)

    def create_order(self , symbol , tag  , side , size:int, tdmode = "cross" , ordType = "market" ):
        data = { "id": tag , "op": "order", "args": [{ "side": side , "instId": symbol , "tdMode": tdmode , "ordType": ordType, "sz": str(size)  }] }
        data_str = json.dumps(data)
        self.logger.info(data_str)
        data_ = { "id": "TADEBUG", "op": "order", "args": [{ "side": "buy", "instId": "LUNA-USDT-SWAP", "tdMode": "cross", "ordType": "market", "sz": "1" }] }

        self.logger.debug(data_)
        self.ws.send(data_str)

    def subscribe_order(self):
        data = {}
        data['op'] = "subscribe"
        data['args'] = []
        sub_info = {}
        sub_info['channel'] = "orders"
        sub_info['instType'] = "SWAP"
        data['args'].append(sub_info)
        data_str = json.dumps(data)
        logger.info("subscribe_order")
        self.ws.send(data_str)

    def subscribe_trades(self,symbol:str):
        data = {}
        data['op'] = "subscribe"
        data['args'] = []
        sub_info = {}
        sub_info['channel'] = "trades"
        sub_info['instId'] = symbol
        data['args'].append(sub_info)
        data_str = json.dumps(data)
        print(data_str)
        logger.info("subscribe_order")
        self.ws.send(data_str)

if __name__ == "__main__":
  #def __init__(self, wsURL, api_key,passphrase , api_secret, on_msg):

  from config import config_okex
  #trader = OkexWebsocket(config_okex.okex_ws_url , config_okex.api_key ,  config_okex.password , config_okex.api_secret , None)
#  import time
#  time.sleep(5)
#  trader.subscribe_order()
#  time.sleep(2)
#  trader.create_order("LUNA-USDT-SWAP" , "TAGDEBUG" , "buy" , 1)
#  trader.test_trade()
  #trader.subscribe_trades("LUNA-USDT")
  trader = OkexWebsocket(config_okex.okex_ws_url_public , None ,  None , None , None)
  trader.subscribe_trades("LUNA-USDT-SWAP")


