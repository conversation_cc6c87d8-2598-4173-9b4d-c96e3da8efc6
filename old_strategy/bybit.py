import os
import config
from bybit_ws import Bybit<PERSON>ebsocket
from log import logger
import threading
import ccxt
import time
from threading import Timer
from easydict import EasyDict
from config import config_bybit
import traceback


class Bybit:
    def __init__(self):
        self.exchange = ccxt.bybit(
            {'apiKey': config_bybit.api_key, 'secret': config_bybit.api_secret, 'enableRateLimit': True})
        self.exchange.load_markets()
        self.bybit_ws = BybitWebsocket(
            wsURL=config_bybit.bybit_ws_url,
            api_key=config_bybit.api_key,
            api_secret=config_bybit.api_secret,
            on_msg=self.on_msg)
        self.bybit_ws.subscribe_execution()
        self.bybit_ws.subscribe_order()

        self.on_finish_func_lists = {}
        self.orders_status = {}
        self.exe_always_callback_func = None

        self.latest_buy_order = None
        self.latest_sell_order = None

    def setup_exe_always_callback_func(self , func):
        self.exe_always_callback_func = func

    def on_msg(self):
        logger.warning("on msg execution")
        data = self.bybit_ws.get_data("execution")
        logger.info(data)
        client_id = data['data'][0]['order_link_id']
        logger.error(self.on_finish_func_lists.keys())
        logger.error(client_id)
        if client_id != "" and client_id.startswith("HG_"):
            logger.error("callback")
            if self.exe_always_callback_func:
                thread = threading.Thread(target=self.exe_always_callback_func, args=(data.copy(),))
            else:
                func = self.on_finish_func_lists[client_id]
                thread = threading.Thread(target=func, args=(data.copy(),))
            thread.start()
            if client_id in self.orders_status.keys():
                self.orders_status[client_id] = True

    def fetch_l2_orderbook(self, symbol: str):
        orderbook = self.exchange.fetch_l2_order_book(symbol, limit=100)
        return orderbook

    def cancel_order(self, order_info):
        symbol = order_info['symbol']
        logger.info("cancel order")
        client_id = order_info['params_dict']['order_link_id']
        try:
            if client_id in self.on_finish_func_lists.keys():
                del self.on_finish_func_lists[client_id]
#          if self.orders_status[client_id]:
#              logger.info("The order which client id %s has filled"%(client_id))
#              del self.on_finish_func_lists[client_id]
#              return
        except:
            logger.error(traceback.format_exc())
        try:
            res = self.exchange.cancel_order(
                None, symbol, params=order_info['params_dict'])
            logger.info(res)
        except BaseException:
            logger.error(traceback.format_exc())
            # pass

    def create_order(
            self,
            symbol: str,
            price: float,
            amt: float,
            side: str,
            reduce_only: bool = False,
            timeout: float = 5,
            on_submit=None,
            on_order_finish=None,
            on_cancel=None,
            tag="HG"):
        client_tag = str(int(time.time() * 1000)) + "_H"
        client_id = tag + "_" + side + "_" + client_tag
        params_dict = {"order_link_id": client_id, "position_idx": 0}
        order_info = {
            "symbol": symbol,
            "price": price,
            "amt": amt,
            "side": side,
            "reduce_only": reduce_only,
            "params_dict": params_dict}
        #order_info = {"symbol":symbol ,"price":price , "amt":amt , "side":side , "reduce_only":reduce_only ,"params_dict":params_dict , "position_idx": 0 }

        if on_order_finish:
            self.on_finish_func_lists[client_id] = on_order_finish
            # logger.debug(self.on_finish_func_lists)
        self.orders_status[client_id] = False
        if side == "BUY":
            res = self.exchange.create_limit_buy_order(
                symbol, amt, price, params=params_dict)
            logger.info(res)
        elif side == "SELL":
            res = self.exchange.create_limit_sell_order(
                symbol, amt, price, params=params_dict)
            logger.info(res)
        else:
            pass
        order_info.update({"ret": res})
        if on_submit:
            on_submit(order_info)

        def _on_cancel(order_info: dict):
            self.cancel_order(order_info)
            if on_cancel:
                on_cancel(order_info)

        if _on_cancel:
            timer_timeout = Timer(timeout, _on_cancel, (order_info,))
            timer_timeout.start()

        return order_info

        # No Implmentation

    def fetch_positions(self, symbol):
        #market = self.exchange.market(symbol)
        #response = self.exchange.private_linear_get_position_list({'symbol':symbol})
        symbol = symbol.replace("/", "")
        market = self.exchange.market(symbol)
        response = self.exchange.private_linear_get_position_list(
            {'symbol': market['id']})
        linear_positions = response['result']
        print(linear_positions)
        all_size = 0.0
        for item in linear_positions:
            if item['symbol'] == symbol:
                if 'side' in item.keys() and item['side'] == 'Buy':
                    size = abs(float(item['size']))
                    all_size += size
                if 'side' in item.keys() and item['side'] == 'Sell':
                    size = -abs(float(item['size']))
                    all_size += size
        return round(all_size, 3)


if __name__ == "__main__":
    bybit = Bybit()

    def on_submit(order_info: dict):
        logger.warning("on_submit")
        logger.info(order_info['ret'])

    def on_order_finish(data: dict):
        logger.warning("on exe")
        logger.warning(data)
    result = bybit.fetch_positions("LUNA/USDT")
    print(result)

#    time.sleep(5)
#
#    symbol = "LUNA/USDT"
#    orderbook = bybit.fetch_l2_orderbook(symbol)
#    print(len(orderbook['bids']))
#    price = 60.0
#    bybit.create_order(symbol , price, 1 , "BUY", False, config.timeout, on_submit = on_submit  , on_order_finish=on_order_finish)
#    while 1:
#        symbol = "XRP/USDT"
#        orderbook = bybit.fetch_l2_orderbook(symbol)
#        price,qty = orderbook['bids'][-1]
#        mid_price = (orderbook['bids'][0][0] + orderbook['asks'][0][0])/2
#        spread = 1 - price/mid_price
#        logger.debug("ready to create limit @ spread %f"%(spread))
#        price = orderbook['bids'][0][0]
#        bybit.create_order(symbol , price, 1 , "BUY", False, config.timeout, on_submit = on_submit  , on_order_finish=on_order_finish)
#        time.sleep(config.timeout)
#
