import os
import config
from bybit_ws import BybitWebsocket
from log import logger
import threading
import ccxt
import time
from threading import Timer
from easydict import EasyDict
import traceback

from config import config_binance
import config


class Binance:
    def __init__(self):
        self.exchange_future = ccxt.binance(
            {
                'apiKey': config_binance.apiKey,
                'secret': config_binance.secret,
                'enableRateLimit': True,
                'options': {
                    'defaultType': 'future',
                },
            })
        markets = self.exchange.load_markets()

    def fetch_l2_orderbook(self, symbol: str):
        orderbook = self.exchange.fetch_l2_order_book(symbol, limit=50)
        return orderbook

    def cancel_order(self, order_info):
        pass
#        symbol = order_info['symbol']
#        logger.info("cancel order")
#        client_id = order_info['params_dict']['order_link_id']
#        try:
#          if self.orders_status[client_id]:
#              logger.info("The order which client id %s has filled"%(client_id))
#              del self.on_finish_func_lists[client_id]
#              return
#          res = self.exchange.cancel_order(None,symbol,params=order_info['params_dict'])
#          logger.info(res)
#          del self.on_finish_func_lists[client_id]
#        except:
#          logger.error(traceback.format_exc())
#          #pass

    def create_market_order(
            self,
            symbol: str,
            amt: float,
            side: str,
            reduce_only: bool = False,
            timeout: float = 5,
            on_submit=None,
            on_order_finish=None,
            on_cancel=None):
        client_id = str(int(time.time() * 1000)) + "H"
        params_dict = {"client_id": client_id}
        order_info = {
            "symbol": symbol,
            "amt": amt,
            "side": side,
            "reduce_only": reduce_only,
            "params_dict": params_dict}
        if side == "BUY":
            res = self.exchange.create_market_buy_order(
                symbol, amt, params=params_dict)
            logger.info(res)
        elif side == "SELL":
            res = self.exchange.create_market_sell_order(
                symbol, amt, params=params_dict)
            logger.info(res)
        else:
            pass
        order_info.update({"ret": res})
        if on_submit:
            on_submit(order_info)

        def _on_cancel(order_info: dict):
            self.cancel_order(order_info)
            if on_cancel:
                on_cancel(order_info)

        if _on_cancel:
            timer_timeout = Timer(timeout, _on_cancel, (order_info,))
            timer_timeout.start()

    def fetch_positions(self, symbol):
        market = self.exchange.market(symbol)
        response = self.exchange.private_linear_get_position_list(
            {'symbol': market['id']})
        linear_positions = response['result']
        return linear_positions


if __name__ == "__main__":
    binance = Okex()
    binance.create_market_order("XRP-USDT-SWAP", 2, "BUY")
    #depth = bybit.fetch_l2_orderbook("XRP/USDT")
#
