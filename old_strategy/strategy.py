from bybit import Bybit
from okex import Okex
from okex_quotation import OkexQuotation
from log import logger
import config
import time
import traceback
from notify import dprint


class CrossMarketMakerStrategy:
    """
    跨市场做市商策略类
    用于在不同交易所之间进行套利交易
    """
    def __init__(self):
        # 初始化基本变量
        self.balance = 0  # 账户余额

        # 持仓相关变量
        self.taker_position = 0  # taker市场的持仓量
        self.maker_position = 0  # maker市场的持仓量
        self.loop_times = 0  # 策略循环次数
        self.latest_maker_buy_order = None  # 最新的maker市场买单
        self.latest_maker_sell_order = None  # 最新的maker市场卖单

        # 时间戳记录
        self.sell_limit_timestamp = 0  # 卖单限制时间戳
        self.buy_limit_timestamp = 0  # 买单限制时间戳

    def setup_symbol(self, symbol: str):
        """设置交易对符号"""
        self.symbol = symbol

    def init_market(self):
        """初始化交易市场"""
        # 初始化maker市场(Bybit)
        self.maker_market = Bybit()
        self.maker_market.setup_exe_always_callback_func(self.on_order_finish)
        # 初始化taker市场(OKEx)
        self.taker_market = Okex()
        # 如果启用保证金保护，初始化OKEx行情订阅
        if config.use_margin_protection:
            self.okex_quotation = OkexQuotation(self.callback_taker_quotation)

    def callback_taker_quotation(self,msg):
        """
        taker市场行情回调函数
        用于监控taker市场价格变动并相应调整maker市场订单
        """
        try:
            data = msg['data']
            for trade in data:
                price = float(trade['px'])  # 成交价格
                sz = float(trade['sz'])  # 成交数量
                side = trade['side']  # 成交方向
                if self.latest_maker_buy_order and self.latest_maker_sell_order:
                    # 当taker市场卖出价格低于maker市场买入价格一定比例时，取消maker买单
                    if side == "sell" and (price/self.latest_maker_buy_order['price'] - 1 < config.taker_cancel_margin) and (time.time() - self.sell_limit_timestamp > config.limit_cancel_time):
                        margin_spread = price/self.latest_maker_buy_order['price'] - 1
                        logger.debug("trigger cancel [SELL] margin_spread:{0}".format(margin_spread))
                        dprint("trigger cancel [SELL] margin_spread:{0}".format(margin_spread))
                        self.sell_limit_timestamp = time.time()
                        self.maker_market.cancel_order(self.latest_maker_buy_order)
                    # 当taker市场买入价格高于maker市场卖出价格一定比例时，取消maker卖单
                    if side == "buy" and (price/self.latest_maker_sell_order['price'] - 1 > -config.taker_cancel_margin) and (time.time() - self.buy_limit_timestamp > config.limit_cancel_time):
                        margin_spread = price/self.latest_maker_sell_order['price'] - 1
                        logger.debug("trigger cancel [BUY] margin_spread:{0}".format(margin_spread))
                        dprint("trigger cancel [BUY] margin_spread:{0}".format(margin_spread))
                        self.buy_limit_timestamp = time.time()
                        self.maker_market.cancel_order(self.latest_maker_sell_order)
                break
        except:
            logger.error(traceback.format_exc())

    def on_order_finish(self,data: dict):
        """
        订单完成回调函数
        当maker市场订单成交后，在taker市场执行对冲交易
        """
        try:
            logger.warning("execution")
            logger.warning(data)
            full_order_qty = 0
            price = -1
            order_link_id = data['data'][0]['order_link_id']
            logger.info("order_info_id:{0}".format(order_link_id))
            if order_link_id.startswith("HG"):
                tag, side, client_id, _ = order_link_id.split("_")
                for one in data['data']:
                    # 累计taker市场成交数量
                    full_order_qty += one['exec_qty']
                    price = one['price']
                # 计算taker市场对冲数量
                taker_amt = full_order_qty * config.taker_scale
                # 在taker市场执行对冲交易
                if side == "BUY":
                    result = self.taker_market.create_market_order(
                        config.taker_symbol, taker_amt, "SELL")
                else:
                    result = self.taker_market.create_market_order(
                        config.taker_symbol, taker_amt, "BUY")

                maker_price = {"price":  price  , "maker_timestamp": int(client_id)}
                self.taker_market.set_comparable_maker_info(maker_price )
                logger.info(result)
        except BaseException:
            logger.error(traceback.format_exc())

    def place_order(self, price: float, side: str):
        """
        下单函数
        在maker市场创建限价单
        """
        def on_submit(order_info: dict):
            logger.warning("on_submit")
            logger.info(order_info['ret'])

        amt = config.single_maker_amt
        if side == "OPEN":
            # 创建买单
            order_info = self.maker_market.create_order(
                config.maker_symbol,
                price,
                amt,
                "BUY",
                False,
                config.timeout,
                on_submit=on_submit,
                on_order_finish=None)
            self.latest_maker_buy_order = order_info
        else:
            # 创建卖单
            order_info = self.maker_market.create_order(
                config.maker_symbol,
                price,
                amt,
                "SELL",
                False,
                config.timeout,
                on_submit=on_submit,
                on_order_finish=None)
            self.latest_maker_sell_order = order_info

    def calc_limit_price(self, orderbook, side: str, maker_spread, maker_depth) -> float:
        """
        计算限价单价格
        根据订单簿深度和价差参数计算最优挂单价格
        """
        depth = maker_depth
        mid_price = (orderbook['bids'][0][0] + orderbook['asks'][0][0]) / 2
        if side == "OPEN":
            bid_price, qty = orderbook['bids'][depth]
            if isinstance(maker_spread, float):
                price = mid_price * (1 - maker_spread)
                logger.info("use target spread")
            if isinstance(maker_spread, str) and maker_spread == "depth":
                price = bid_price
                logger.info("use level")
        else:
            ask_price, qty = orderbook['asks'][depth]
            if isinstance(maker_spread, float):
                price = mid_price * (1 + maker_spread)
                logger.info("use target spread")
            if isinstance(maker_spread, str) and maker_spread == "depth":
                price = ask_price
                logger.info("use level")
        spread = 1 - price / mid_price
        logger.debug("ready to create limit @ spread %f" % (spread))
        return price

    def run_once(self, symbol: str, side: str, maker_spread, maker_depth):
        """
        执行一次交易
        获取订单簿并下单
        """
        order_book = self.maker_market.fetch_l2_orderbook(symbol)
        price = self.calc_limit_price(
            order_book, side, maker_spread, maker_depth)
        self.place_order(price, side)

    def update_position(self):
        """
        更新持仓信息
        从两个市场获取最新持仓数据
        """
        try:
            self.maker_position = self.maker_market.fetch_positions(
                config.maker_symbol)
            self.taker_position = self.taker_market.fetch_positions(
                config.taker_symbol)
            return True
        except BaseException:
            logger.error(traceback.format_exc())
            return False

    def check_balance(self):
        """
        检查持仓平衡性
        计算两个市场的持仓比例，确保风险可控
        """
        mid = (abs(self.maker_position) + abs(self.maker_position)) / 2.0
        inventory_ratio = abs(
            (self.maker_position + self.taker_position) / (mid + config.eps))
        inventory_ratio = round(inventory_ratio, 3)
        if inventory_ratio > config.inventry_risk_ratio:
            content = "single leg risk ,inventory_ratio:{0} maker_size:{1} , taker_size:{2}".format(
                inventory_ratio, self.maker_position, self.taker_position)
            logger.error(content)
            dprint(content)
        else:
            logger.info(
                "risk checking [OK], inventory_ratio:{0} maker_size:{1} , taker_size:{2}".format(
                    inventory_ratio,
                    self.maker_position,
                    self.taker_position))
        return inventory_ratio

    def fetch_depth(self , maker_symbol:str , taker_symbol:str):
        """
        获取两个市场的深度数据
        计算开仓和平仓的价差
        """
        taker_depth= self.taker_market.fetch_l2_orderbook(taker_symbol)
        maker_depth= self.maker_market.fetch_l2_orderbook(maker_symbol)
        taker_bid = taker_depth['bids']
        taker_ask = taker_depth['asks']
        maker_bid = maker_depth['bids']
        maker_ask = maker_depth['asks']
        spread_open = taker_bid[0][0]/maker_bid[0][0] - 1
        logger.debug("spread open {0}".format(spread_open))
        spread_close = maker_ask[0][0]/taker_ask[0][0] - 1
        logger.debug("spread close {0}".format(spread_close))
        return spread_open,spread_close

    def strategy_loop(self):
        """
        策略主循环V1 等砸盘
        基础版本，根据持仓情况直接下单
        """
        while True:
            status = self.update_position()
            if status:
                if self.check_balance() <= 0 :
                    if self.maker_position + config.single_maker_amt < config.max_position + config.eps:
                        self.run_once(
                            config.maker_symbol,
                            "OPEN",
                            config.maker_spread,
                            config.maker_depth)
                    if self.maker_position - config.single_maker_amt > - \
                            (config.max_position + config.eps):
                        self.run_once(
                            config.maker_symbol,
                            "CLOSE",
                            config.maker_spread,
                            config.maker_depth)
            time.sleep(config.timeout)

    def strategy_loop_v2(self):
        """
        策略主循环V2
        增强版本，增加了价差检查机制
        """
        while True:
            status = self.update_position()
            if status:
                self.loop_times += 1
                if self.check_balance() <= 0 and self.maker_position + config.single_maker_amt < config.max_position + config.eps:
                    self.spread_open_counter = 0
                    self.spread_close_counter = 0
                    # 多次检查价差
                    for x in range(config.spread_check_times):
                        time.sleep(config.spread_check_delay)
                        spread_open,spread_close = self.fetch_depth( config.maker_symbol , config.taker_symbol)
                        logger.info("check spread [{0}/{1}] result spread_open:{2} spread_close:{3}".format(x+1, config.spread_check_times , round(spread_open,5), round(spread_close,5)))
                        if spread_open  > config.spread_limit:
                            self.spread_open_counter += 1
                            logger.debug("spread_open_counter:{0} spread_close_counter:{1}".format(self.spread_open_counter , self.spread_close_counter))
                        if spread_close > config.spread_limit:
                            self.spread_close_counter += 1
                            logger.debug("spread_open_counter:{0} spread_close_counter:{1}".format(self.spread_open_counter , self.spread_close_counter))
                    # 根据价差检查结果决定是否开仓
                    if self.spread_open_counter == config.spread_check_times:
                        logger.debug("spread_open_counter:{0} . Create [OPEN] Order".format(self.spread_open_counter))
                        self.run_once( config.maker_symbol, "OPEN", config.maker_spread, config.maker_depth)

                    if self.spread_close_counter == config.spread_check_times:
                        logger.debug("spread_close_counter:{0} . Create [CLOSE] Order".format(self.spread_close_counter))
                        self.run_once( config.maker_symbol, "CLOSE", config.maker_spread, config.maker_depth)
            time.sleep(config.timeout)


if __name__ == "__main__":
    # 程序入口
    strategy = CrossMarketMakerStrategy()
    strategy.init_market()
    # 根据配置选择使用哪个版本的策略循环
    if config.use_spread_control:
        strategy.strategy_loop_v2()
    else:
        strategy.strategy_loop()
