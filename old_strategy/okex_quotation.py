
import os
import config
from bybit_ws import BybitWebsocket
from log import logger
from notify import dprint
import threading
import ccxt
import time
from threading import Timer
from easydict import EasyDict
import traceback

from config import config_okex
import config
from okex_ws import OkexWebsocket


class OkexQuotation:
    def __init__(self,callback):
        self.exchange = ccxt.okex5({'apiKey': config_okex.api_key,
                                    'secret': config_okex.api_secret,
                                    'password': config_okex.password,
                                    'options': {'defaultType': 'swap'}})
        markets = self.exchange.load_markets()
        self.exchange_ws = OkexWebsocket(config_okex.okex_ws_url_public ,None ,  None , None ,self.on_ws_msg )
        self.exchange_ws.subscribe_trades(config.taker_symbol)
        self.callback = callback

    def on_ws_msg(self,msg):
        if 'arg' in msg.keys() and msg['arg']['channel'] == 'trades' and msg['arg']['instId'] == config.taker_symbol:
            data = msg['data']
            self.callback(msg)
            for trade in data:
                price = trade['px']
                sz = trade['sz']
                side = trade['side']


if __name__ == "__main__":
    def callback(msg):
        data = msg['data']
        print(msg)
        for trade in data:
            price = trade['px']
            sz = trade['sz']
            side = trade['side']

    okex = OkexQuotation(callback)
