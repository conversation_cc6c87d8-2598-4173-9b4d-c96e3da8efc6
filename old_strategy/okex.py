import os
import config
from bybit_ws import BybitWebsocket
from log import logger
from notify import dprint
import threading
import ccxt
import time
from threading import Timer
from easydict import EasyDict
import traceback

from config import config_okex
import config
from okex_ws import OkexWebsocket


class Okex:
    def __init__(self):
        #self.exchange = ccxt.okex({ 'apiKey':config_okex.api_key , 'secret': config_okex.api_secret , "password":config_okex.password, 'enableRateLimit': True})
        self.exchange = ccxt.okex5({'apiKey': config_okex.api_key,
                                    'secret': config_okex.api_secret,
                                    'password': config_okex.password,
                                    'options': {'defaultType': 'swap'}})

        markets = self.exchange.load_markets()
        self.exchange_ws = OkexWebsocket(config_okex.okex_ws_url , config_okex.api_key ,  config_okex.password , config_okex.api_secret ,self.on_ws_msg )
        time.sleep(1)
        self.exchange_ws.subscribe_order()
        time.sleep(1)
        self.latest_taker_info = None
        self.comparable_maker_info = None



    def set_comparable_maker_info(self, maker_info):
        logger.debug("set comparable_maker_info %s"%(str(maker_info)))
        self.comparable_maker_info = maker_info

    def on_ws_msg(self,msg):
        try:
            logger.info("on msg: \n {0}".format(str(msg)))
            if 'arg' in msg.keys() and msg['arg']['channel'] == 'orders':
              #logger.error('arg' in msg.keys() , msg['arg']['channel'] == 'orders')
                datas = msg['data']
                if len(datas) > 0 :
                  real_data = datas[0]
                  if int(real_data['accFillSz']) > 0 :
                      avg_price = float(real_data['avgPx'])
                      side = real_data['side']
                      size = float(real_data['fillSz'])
                      fill_time= int(real_data['fillTime'])
                      if self.comparable_maker_info:
                          spread = -1
                          if side == "buy":
                              maker_price = float(self.comparable_maker_info['price'])
                              spread = (maker_price/avg_price - 1)
                          if side == "sell":
                              maker_price = float(self.comparable_maker_info['price'])
                              spread = (avg_price/maker_price - 1)
                          maker_timestamp = float(self.comparable_maker_info['maker_timestamp'])
                          value = round(avg_price * size , 4)
                          profit = spread*value
                          latency =  fill_time - maker_timestamp
                          note = "taker filled. \n side:%s \n spread:%.4f \n profit:%.4f \n mk_price:%.4f \n tk_price:%.4f \n mk_tk_time:%d ms"%(side,spread,profit, maker_price , avg_price  , latency)
                          logger.warning(note)
                          dprint(note)
        except:
            logger.error(traceback.format_exc())

    def fetch_l2_orderbook(self, symbol: str):
        orderbook = self.exchange.fetch_l2_order_book(symbol, limit=50)
        return orderbook

    def cancel_order(self, order_info):
        pass
#        symbol = order_info['symbol']
#        logger.info("cancel order")
#        client_id = order_info['params_dict']['order_link_id']
#        try:
#          if self.orders_status[client_id]:
#              logger.info("The order which client id %s has filled"%(client_id))
#              del self.on_finish_func_lists[client_id]
#              return
#          res = self.exchange.cancel_order(None,symbol,params=order_info['params_dict'])
#          logger.info(res)
#          del self.on_finish_func_lists[client_id]
#        except:
#          logger.error(traceback.format_exc())
#          #pass

    def create_market_order(
            self,
            symbol: str,
            amt: float,
            side: str,
            reduce_only: bool = False,
            timeout: float = 5,
            on_submit=None,
            on_order_finish=None,
            on_cancel=None):
        client_id = str(int(time.time() * 1000)) + "H"
        params_dict = {"tag": client_id, "tdMode": "cross"}
        order_info = {
            "symbol": symbol,
            "amt": amt,
            "side": side,
            "reduce_only": reduce_only,
            "params_dict": params_dict,
            "sz": str(amt)}
#        if on_order_finish:
#            self.on_finish_func_lists[client_id] = on_order_finish
#            logger.debug(self.on_finish_func_lists)
#        self.orders_status[client_id] = False
        res = None
        logger.debug(order_info)
        logger.debug("taker order method: %s"%(config.taker_order_method))
        if config.taker_order_method == "rs":
            if side == "BUY":
                res = self.exchange.create_market_buy_order(
                    symbol, amt, params=params_dict)
                logger.info(res)
            elif side == "SELL":
                res = self.exchange.create_market_sell_order(
                    symbol, amt, params=params_dict)
                logger.info(res)
            else:
                pass
            order_info.update({"ret": res})
        else:
            self.exchange_ws.create_order(symbol, client_id, side.lower() , int(amt))

        if on_submit:
            on_submit(order_info)

        def _on_cancel(order_info: dict):
            self.cancel_order(order_info)
            if on_cancel:
                on_cancel(order_info)

        if _on_cancel:
            timer_timeout = Timer(timeout, _on_cancel, (order_info,))
            timer_timeout.start()

        return res

    def fetch_positions(self, symbol):
        positions = self.exchange.fetch_positions(symbol)
        all_size = 0.0
        for item in positions:
            contract_size = float(item['contractSize'])
            if item['side'] == 'short':
                all_size += -abs(item['contracts']) * contract_size
            else:
                all_size += abs(item['contracts']) * contract_size
        return round(all_size, 3)


if __name__ == "__main__":
    okex = Okex()
    okex.create_market_order("LUNA-USDT-SWAP" ,  1, "BUY")
    okex.create_market_order("LUNA-USDT-SWAP" ,  1, "SELL")
    okex.set_comparable_maker_info({"price": 49})

    #res = okex.fetch_positions("LUNA-USDT-PERP")
    #print(res)
    # print(res)
    #depth = bybit.fetch_l2_orderbook("XRP/USDT")
#
