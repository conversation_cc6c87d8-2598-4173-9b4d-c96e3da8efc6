
# max_position_allow =
#symbol = ""

from easydict import EasyDict as edict


config_okex = edict()
config_okex.api_key = 'c568d840-2392-4b3d-ba1d-591137708f1b'
config_okex.api_secret = 'DC20491053CDAB870430E259ED4EAF35'
config_okex.password = '10qpalzm'
config_okex.okex_ws_url= "wss://wsaws.okex.com:8443/ws/v5/private"
config_okex.okex_ws_url_public= "wss://wsaws.okex.com:8443/ws/v5/public"

config_bybit = edict()
config_bybit.api_key = "wbmSuQBjfP8fBoF1yW"
config_bybit.api_secret = "1W1S7lgcXZ33PCHcwL9wQJpt3oooiWOoiVGw"
config_bybit.bybit_ws_url = "wss://stream.bybit.com/realtime_private"


dingtalk_token = "94b98b003c76d304d05406e5897daef8a35edcc97c414398a71bb8af54511bdc"

maker_makret = "bybit"
taker_market = "okex"


scale = 1

single_maker_amt = 0.1 * scale

taker_scale = 10

maker_symbol = "LUNA/USDT"

taker_symbol = "LUNA-USDT-SWAP"

#maker_spread = "depth"
maker_spread = 0.003
#maker_spread = 0.0025

maker_depth = 1

timeout = 2

side = "OPEN"

full_amt = 1.0

max_position = 10.0

inventry_risk_ratio = 0.01  # single leg risk

use_margin_protection = True 

taker_cancel_margin = 0.0015

eps = 1e-3

allow_open = True

allow_close = True

taker_order_method = "ws"  # ws / rs

spread_check = True

spread_check_times = 3

spread_check_delay = 0.5 # 1s

spread_limit = 0.0005

use_spread_control = False

limit_cancel_time = 1

